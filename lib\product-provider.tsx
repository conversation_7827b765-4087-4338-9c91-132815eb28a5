"use client"

import React, { createContext, useContext, useState, useCallback, useEffect } from "react"
import { v4 as uuidv4 } from "uuid"
import { useToast } from "@/components/ui/use-toast"
import { Product, ProductCategory, ProductTransfer, beautyProducts, defaultProductCategories } from "./products-data"

// Product storage keys
const STORAGE_KEYS = {
  PRODUCTS: 'salon_products',
  CATEGORIES: 'salon_product_categories',
  TRANSFERS: 'salon_product_transfers'
}

// Context type definition
interface ProductContextType {
  // Products
  products: Product[]
  getProductById: (id: string) => Product | undefined
  getProductsByCategory: (category: string) => Product[]
  getRetailProducts: () => Product[]
  addProduct: (productData: Omit<Product, "id" | "createdAt" | "updatedAt">) => Product
  updateProduct: (updatedProduct: Product) => void
  deleteProduct: (productId: string) => boolean
  refreshProducts: () => void

  // Categories
  categories: ProductCategory[]
  getCategoryById: (id: string) => ProductCategory | undefined
  getCategoryName: (id: string) => string
  addCategory: (categoryData: Omit<ProductCategory, "id" | "createdAt" | "updatedAt">) => ProductCategory
  updateCategory: (updatedCategory: ProductCategory) => void
  deleteCategory: (categoryId: string) => boolean
  refreshCategories: () => void

  // Transfers
  transfers: ProductTransfer[]
  getTransferById: (id: string) => ProductTransfer | undefined
  getTransfersByProduct: (productId: string) => ProductTransfer[]
  createTransfer: (transferData: Omit<ProductTransfer, "id" | "createdAt">) => ProductTransfer
  updateTransfer: (updatedTransfer: ProductTransfer) => void
  completeTransfer: (transferId: string) => boolean
  cancelTransfer: (transferId: string) => boolean
}

// Create context with default values
const ProductContext = createContext<ProductContextType>({
  products: [],
  getProductById: () => undefined,
  getProductsByCategory: () => [],
  getRetailProducts: () => [],
  addProduct: () => ({} as Product),
  updateProduct: () => {},
  deleteProduct: () => false,
  refreshProducts: () => {},

  categories: [],
  getCategoryById: () => undefined,
  getCategoryName: () => "Uncategorized",
  addCategory: () => ({} as ProductCategory),
  updateCategory: () => {},
  deleteCategory: () => false,
  refreshCategories: () => {},

  transfers: [],
  getTransferById: () => undefined,
  getTransfersByProduct: () => [],
  createTransfer: () => ({} as ProductTransfer),
  updateTransfer: () => {},
  completeTransfer: () => false,
  cancelTransfer: () => false,
})

// Storage utilities
const getFromStorage = <T>(key: string, defaultValue: T): T => {
  if (typeof window === 'undefined') return defaultValue
  try {
    const item = localStorage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch (error) {
    console.error(`Error reading ${key} from localStorage:`, error)
    return defaultValue
  }
}

const saveToStorage = <T>(key: string, value: T): void => {
  if (typeof window === 'undefined') return
  try {
    localStorage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error(`Error saving ${key} to localStorage:`, error)
  }
}

export function ProductProvider({ children }: { children: React.ReactNode }) {
  const { toast } = useToast()
  const [products, setProducts] = useState<Product[]>([])
  const [categories, setCategories] = useState<ProductCategory[]>([])
  const [transfers, setTransfers] = useState<ProductTransfer[]>([])
  const [isInitialized, setIsInitialized] = useState(false)

  // Initialize data from storage
  useEffect(() => {
    if (isInitialized) return

    try {
      // Load products - merge with default beauty products
      const storedProducts = getFromStorage<Product[]>(STORAGE_KEYS.PRODUCTS, [])
      const mergedProducts = [...beautyProducts, ...storedProducts]
      setProducts(mergedProducts)

      // Load categories - merge with default categories
      const storedCategories = getFromStorage<ProductCategory[]>(STORAGE_KEYS.CATEGORIES, [])
      const mergedCategories = [...defaultProductCategories, ...storedCategories]
      setCategories(mergedCategories)

      // Load transfers
      const storedTransfers = getFromStorage<ProductTransfer[]>(STORAGE_KEYS.TRANSFERS, [])
      setTransfers(storedTransfers)

      setIsInitialized(true)
    } catch (error) {
      console.error("Error initializing product data:", error)
      setProducts(beautyProducts)
      setCategories(defaultProductCategories)
      setTransfers([])
      setIsInitialized(true)
    }
  }, [isInitialized])

  // Product methods
  const getProductById = useCallback((id: string) => {
    return products.find(product => product.id === id)
  }, [products])

  const getProductsByCategory = useCallback((category: string) => {
    return products.filter(product => product.category === category)
  }, [products])

  const getRetailProducts = useCallback(() => {
    return products.filter(product => product.isRetail && product.isActive !== false)
  }, [products])

  const addProduct = useCallback((productData: Omit<Product, "id" | "createdAt" | "updatedAt">) => {
    const newProduct: Product = {
      id: uuidv4(),
      ...productData,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    setProducts(prev => {
      const updated = [...prev, newProduct]
      saveToStorage(STORAGE_KEYS.PRODUCTS, updated.filter(p => !beautyProducts.find(bp => bp.id === p.id)))
      return updated
    })

    // Update category product count
    if (productData.category) {
      const category = categories.find(c => c.name === productData.category)
      if (category) {
        const updatedCategory = {
          ...category,
          productCount: category.productCount + 1,
          updatedAt: new Date()
        }
        updateCategory(updatedCategory)
      }
    }

    return newProduct
  }, [categories])

  const updateProduct = useCallback((updatedProduct: Product) => {
    setProducts(prev => {
      const updated = prev.map(product =>
        product.id === updatedProduct.id
          ? { ...updatedProduct, updatedAt: new Date() }
          : product
      )
      saveToStorage(STORAGE_KEYS.PRODUCTS, updated.filter(p => !beautyProducts.find(bp => bp.id === p.id)))
      return updated
    })
  }, [])

  const deleteProduct = useCallback((productId: string) => {
    const productToDelete = products.find(p => p.id === productId)
    if (!productToDelete) return false

    setProducts(prev => {
      const updated = prev.filter(product => product.id !== productId)
      saveToStorage(STORAGE_KEYS.PRODUCTS, updated.filter(p => !beautyProducts.find(bp => bp.id === p.id)))
      return updated
    })

    // Update category product count
    if (productToDelete.category) {
      const category = categories.find(c => c.name === productToDelete.category)
      if (category) {
        const updatedCategory = {
          ...category,
          productCount: Math.max(0, category.productCount - 1),
          updatedAt: new Date()
        }
        updateCategory(updatedCategory)
      }
    }

    return true
  }, [products, categories])

  const refreshProducts = useCallback(() => {
    const storedProducts = getFromStorage<Product[]>(STORAGE_KEYS.PRODUCTS, [])
    const mergedProducts = [...beautyProducts, ...storedProducts]
    setProducts(mergedProducts)
  }, [])

  // Category methods
  const getCategoryById = useCallback((id: string) => {
    return categories.find(category => category.id === id)
  }, [categories])

  const getCategoryName = useCallback((id: string) => {
    const category = categories.find(c => c.id === id || c.name === id)
    return category?.name || "Uncategorized"
  }, [categories])

  const addCategory = useCallback((categoryData: Omit<ProductCategory, "id" | "createdAt" | "updatedAt">) => {
    const newCategory: ProductCategory = {
      id: uuidv4(),
      ...categoryData,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    setCategories(prev => {
      const updated = [...prev, newCategory]
      saveToStorage(STORAGE_KEYS.CATEGORIES, updated.filter(c => !defaultProductCategories.find(dc => dc.id === c.id)))
      return updated
    })

    return newCategory
  }, [])

  const updateCategory = useCallback((updatedCategory: ProductCategory) => {
    setCategories(prev => {
      const updated = prev.map(category =>
        category.id === updatedCategory.id
          ? { ...updatedCategory, updatedAt: new Date() }
          : category
      )
      saveToStorage(STORAGE_KEYS.CATEGORIES, updated.filter(c => !defaultProductCategories.find(dc => dc.id === c.id)))
      return updated
    })
  }, [])

  const deleteCategory = useCallback((categoryId: string) => {
    const categoryToDelete = categories.find(c => c.id === categoryId)
    if (!categoryToDelete) return false

    // Check if category has products
    const productsInCategory = products.filter(p => p.category === categoryToDelete.name)
    if (productsInCategory.length > 0) {
      toast({
        variant: "destructive",
        title: "Cannot delete category",
        description: `This category contains ${productsInCategory.length} product(s). Please move or delete the products first.`,
      })
      return false
    }

    setCategories(prev => {
      const updated = prev.filter(category => category.id !== categoryId)
      saveToStorage(STORAGE_KEYS.CATEGORIES, updated.filter(c => !defaultProductCategories.find(dc => dc.id === c.id)))
      return updated
    })

    return true
  }, [categories, products, toast])

  const refreshCategories = useCallback(() => {
    const storedCategories = getFromStorage<ProductCategory[]>(STORAGE_KEYS.CATEGORIES, [])
    const mergedCategories = [...defaultProductCategories, ...storedCategories]
    setCategories(mergedCategories)
  }, [])

  // Transfer methods
  const getTransferById = useCallback((id: string) => {
    return transfers.find(transfer => transfer.id === id)
  }, [transfers])

  const getTransfersByProduct = useCallback((productId: string) => {
    return transfers.filter(transfer => transfer.productId === productId)
  }, [transfers])

  const createTransfer = useCallback((transferData: Omit<ProductTransfer, "id" | "createdAt">) => {
    const newTransfer: ProductTransfer = {
      id: uuidv4(),
      ...transferData,
      createdAt: new Date()
    }

    setTransfers(prev => {
      const updated = [...prev, newTransfer]
      saveToStorage(STORAGE_KEYS.TRANSFERS, updated)
      return updated
    })

    return newTransfer
  }, [])

  const updateTransfer = useCallback((updatedTransfer: ProductTransfer) => {
    setTransfers(prev => {
      const updated = prev.map(transfer =>
        transfer.id === updatedTransfer.id ? updatedTransfer : transfer
      )
      saveToStorage(STORAGE_KEYS.TRANSFERS, updated)
      return updated
    })
  }, [])

  const completeTransfer = useCallback((transferId: string) => {
    const transfer = transfers.find(t => t.id === transferId)
    if (!transfer || transfer.status !== 'pending') return false

    const updatedTransfer = {
      ...transfer,
      status: 'completed' as const,
      completedAt: new Date()
    }

    updateTransfer(updatedTransfer)

    // Update product inventory levels would happen here in a real app
    toast({
      title: "Transfer completed",
      description: `${transfer.quantity} units of ${transfer.productName} transferred successfully.`,
    })

    return true
  }, [transfers, updateTransfer, toast])

  const cancelTransfer = useCallback((transferId: string) => {
    const transfer = transfers.find(t => t.id === transferId)
    if (!transfer || transfer.status !== 'pending') return false

    const updatedTransfer = {
      ...transfer,
      status: 'cancelled' as const
    }

    updateTransfer(updatedTransfer)

    toast({
      title: "Transfer cancelled",
      description: `Transfer of ${transfer.productName} has been cancelled.`,
    })

    return true
  }, [transfers, updateTransfer, toast])

  return (
    <ProductContext.Provider
      value={{
        products,
        getProductById,
        getProductsByCategory,
        getRetailProducts,
        addProduct,
        updateProduct,
        deleteProduct,
        refreshProducts,

        categories,
        getCategoryById,
        getCategoryName,
        addCategory,
        updateCategory,
        deleteCategory,
        refreshCategories,

        transfers,
        getTransferById,
        getTransfersByProduct,
        createTransfer,
        updateTransfer,
        completeTransfer,
        cancelTransfer,
      }}
    >
      {children}
    </ProductContext.Provider>
  )
}

export const useProducts = () => useContext(ProductContext)
