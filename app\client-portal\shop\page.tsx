"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { useProducts } from "@/lib/product-provider"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Slider } from "@/components/ui/slider"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { ClientPortalLayout } from "@/components/client-portal/client-portal-layout"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import {
  Search,
  ShoppingBag,
  Heart,
  Star,
  Filter,
  ChevronDown,
  SlidersHorizontal,
  Loader2
} from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet"
import { Product } from "@/lib/products-data"

// Import beauty products from our shared data store
import { beautyProducts } from "@/lib/products-data"

export default function ShopPage() {
  const { toast } = useToast()
  const { getRetailProducts } = useProducts()
  const [products, setProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [priceRange, setPriceRange] = useState([0, 50])
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const [sortBy, setSortBy] = useState("featured")
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false)

  // Load retail products from the product provider
  useEffect(() => {
    const retailProducts = getRetailProducts()
    setProducts(retailProducts)
  }, [getRetailProducts])

  // Get unique categories and types for filters
  const categories = [...new Set(products.map(p => p.category))]
  const types = [...new Set(products.map(p => p.type))]

  // Filter products based on search, price range, categories, and types
  const filteredProducts = products.filter(product => {
    // Search filter
    if (searchQuery && !product.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !product.description.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false
    }

    // Price filter
    if (product.price < priceRange[0] || product.price > priceRange[1]) {
      return false
    }

    // Category filter
    if (selectedCategories.length > 0 && !selectedCategories.includes(product.category.toLowerCase())) {
      return false
    }

    // Type filter
    if (selectedTypes.length > 0 && !selectedTypes.includes(product.type.toLowerCase())) {
      return false
    }

    return true
  })

  // Sort products
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case "price-low-high":
        return (a.salePrice || a.price) - (b.salePrice || b.price)
      case "price-high-low":
        return (b.salePrice || b.price) - (a.salePrice || a.price)
      case "rating":
        return (b.rating || 0) - (a.rating || 0)
      case "newest":
        return a.isNew ? -1 : b.isNew ? 1 : 0
      default: // featured
        return (b.isBestSeller ? 1 : 0) - (a.isBestSeller ? 1 : 0)
    }
  })

  const handleCategoryToggle = (category: string) => {
    setSelectedCategories(prev =>
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    )
  }

  const handleTypeToggle = (type: string) => {
    setSelectedTypes(prev =>
      prev.includes(type)
        ? prev.filter(t => t !== type)
        : [...prev, type]
    )
  }

  const handleAddToCart = async (product: Product) => {
    // In a real app, we would store this in localStorage or a state management solution
    // and sync with the server

    // Check if we have enough stock
    if (product.stock <= 0) {
      toast({
        title: "Out of stock",
        description: `Sorry, ${product.name} is currently out of stock.`,
        variant: "destructive"
      });
      return;
    }

    try {
      // Add to cart - this would update a cart state and potentially call an API
      // For now, we'll just show a toast
      toast({
        title: "Added to cart",
        description: `${product.name} has been added to your cart.`,
      });

      // Calculate loyalty points (5 points per $1 spent on products)
      const pointsEarned = Math.floor(product.price * 5);

      // Get client ID from localStorage
      const clientId = localStorage.getItem("client_id") || "client123";

      // Add loyalty points
      const response = await fetch('/api/client-portal/loyalty', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          clientId,
          points: pointsEarned,
          description: `Product Purchase: ${product.name}`
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Show a toast with the points earned
        toast({
          title: "Loyalty Points Earned!",
          description: `You earned ${pointsEarned} loyalty points for this purchase.`,
          variant: "success",
        });

        // If the client reached a new tier, show a special toast
        if (data.tierUpdated) {
          toast({
            title: "Tier Upgraded!",
            description: `Congratulations! You've reached ${data.newTier} tier.`,
            variant: "success",
          });
        }
      }
    } catch (error) {
      console.error("Error processing purchase:", error);
    }
  }

  const handleAddToWishlist = (product: Product) => {
    // In a real app, we would store this in a user's wishlist
    toast({
      title: "Added to wishlist",
      description: `${product.name} has been added to your wishlist.`,
    });
  }

  const clearFilters = () => {
    setSearchQuery("")
    setPriceRange([0, 50])
    setSelectedCategories([])
    setSelectedTypes([])
  }

  return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-2xl font-bold mb-1">Shop Products</h1>
            <p className="text-gray-600">Browse our collection of professional beauty products</p>
          </div>
          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-full md:w-[250px]"
              />
            </div>

            <Sheet open={isMobileFilterOpen} onOpenChange={setIsMobileFilterOpen}>
              <SheetTrigger asChild>
                <Button variant="outline" size="icon" className="md:hidden">
                  <Filter className="h-4 w-4" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-[300px] sm:w-[350px]">
                <SheetHeader className="mb-6">
                  <SheetTitle>Filters</SheetTitle>
                  <SheetDescription>
                    Refine your product search
                  </SheetDescription>
                </SheetHeader>

                <div className="space-y-6">
                  <div>
                    <h3 className="font-medium mb-3">Price Range</h3>
                    <div className="px-2">
                      <Slider
                        value={priceRange}
                        min={0}
                        max={50}
                        step={1}
                        onValueChange={setPriceRange}
                      />
                      <div className="flex justify-between mt-2 text-sm">
                        <span><CurrencyDisplay amount={priceRange[0]} /></span>
                        <span><CurrencyDisplay amount={priceRange[1]} /></span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium mb-3">Categories</h3>
                    <div className="space-y-2">
                      {categories.map((category) => (
                        <div key={category} className="flex items-center space-x-2">
                          <Checkbox
                            id={`category-${category}-mobile`}
                            checked={selectedCategories.includes(category.toLowerCase())}
                            onCheckedChange={() => handleCategoryToggle(category.toLowerCase())}
                          />
                          <Label htmlFor={`category-${category}-mobile`} className="capitalize">
                            {category}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium mb-3">Product Type</h3>
                    <div className="space-y-2">
                      {types.map((type) => (
                        <div key={type} className="flex items-center space-x-2">
                          <Checkbox
                            id={`type-${type}-mobile`}
                            checked={selectedTypes.includes(type.toLowerCase())}
                            onCheckedChange={() => handleTypeToggle(type.toLowerCase())}
                          />
                          <Label htmlFor={`type-${type}-mobile`} className="capitalize">
                            {type}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={clearFilters}
                  >
                    Clear All Filters
                  </Button>

                  <Button
                    className="w-full bg-pink-600 hover:bg-pink-700"
                    onClick={() => setIsMobileFilterOpen(false)}
                  >
                    Apply Filters
                  </Button>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-8">
          {/* Desktop Filters */}
          <div className="hidden md:block w-64 flex-shrink-0">
            <div className="sticky top-24 space-y-6">
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-medium">Filters</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 px-2 text-pink-600"
                    onClick={clearFilters}
                  >
                    Clear All
                  </Button>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium mb-3 flex items-center">
                      Price Range
                      <ChevronDown className="ml-auto h-4 w-4" />
                    </h4>
                    <div className="px-2">
                      <Slider
                        value={priceRange}
                        min={0}
                        max={50}
                        step={1}
                        onValueChange={setPriceRange}
                      />
                      <div className="flex justify-between mt-2 text-sm">
                        <span><CurrencyDisplay amount={priceRange[0]} /></span>
                        <span><CurrencyDisplay amount={priceRange[1]} /></span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-3 flex items-center">
                      Categories
                      <ChevronDown className="ml-auto h-4 w-4" />
                    </h4>
                    <div className="space-y-2">
                      {categories.map((category) => (
                        <div key={category} className="flex items-center space-x-2">
                          <Checkbox
                            id={`category-${category}`}
                            checked={selectedCategories.includes(category.toLowerCase())}
                            onCheckedChange={() => handleCategoryToggle(category.toLowerCase())}
                          />
                          <Label htmlFor={`category-${category}`} className="capitalize">
                            {category}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-3 flex items-center">
                      Product Type
                      <ChevronDown className="ml-auto h-4 w-4" />
                    </h4>
                    <div className="space-y-2">
                      {types.map((type) => (
                        <div key={type} className="flex items-center space-x-2">
                          <Checkbox
                            id={`type-${type}`}
                            checked={selectedTypes.includes(type.toLowerCase())}
                            onCheckedChange={() => handleTypeToggle(type.toLowerCase())}
                          />
                          <Label htmlFor={`type-${type}`} className="capitalize">
                            {type}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Product Grid */}
          <div className="flex-1">
            <div className="flex justify-between items-center mb-6">
              <p className="text-sm text-gray-500">
                Showing {sortedProducts.length} of {products.length} products
              </p>

              <div className="flex items-center gap-2">
                <SlidersHorizontal className="h-4 w-4 text-gray-500" />
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="text-sm border-none bg-transparent focus:ring-0"
                >
                  <option value="featured">Featured</option>
                  <option value="price-low-high">Price: Low to High</option>
                  <option value="price-high-low">Price: High to Low</option>
                  <option value="rating">Top Rated</option>
                  <option value="newest">Newest</option>
                </select>
              </div>
            </div>

            {isLoading ? (
              <div className="flex justify-center items-center py-20">
                <Loader2 className="h-8 w-8 text-pink-600 animate-spin" />
                <span className="ml-2 text-lg text-gray-600">Loading products...</span>
              </div>
            ) : sortedProducts.length === 0 ? (
              <div className="text-center py-12 bg-gray-50 rounded-lg">
                <ShoppingBag className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No products found</h3>
                <p className="text-gray-500 mb-6 max-w-md mx-auto">
                  We couldn't find any products matching your criteria. Try adjusting your filters or search query.
                </p>
                <Button variant="outline" onClick={clearFilters}>
                  Clear Filters
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {sortedProducts.map((product) => (
                  <Card key={product.id} className="overflow-hidden group">
                    <CardContent className="p-0">
                      <div className="relative h-64 overflow-hidden">
                        <Image
                          src={product.image}
                          alt={product.name}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                          priority={true}
                        />
                        <div className="absolute top-3 left-3 flex flex-col gap-2">
                          {product.isNew && (
                            <Badge className="bg-pink-600">New</Badge>
                          )}
                          {product.isBestSeller && (
                            <Badge className="bg-amber-500">Best Seller</Badge>
                          )}
                          {product.isSale && (
                            <Badge className="bg-red-500">Sale</Badge>
                          )}
                        </div>
                        <div className="absolute top-3 right-3">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 rounded-full bg-white/80 hover:bg-white text-gray-600 hover:text-pink-600"
                            onClick={() => handleAddToWishlist(product)}
                          >
                            <Heart className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="absolute inset-x-0 bottom-0 h-12 bg-gradient-to-t from-black/60 to-transparent" />
                      </div>

                      <div className="p-4">
                        <div className="flex items-center gap-1 mb-1">
                          <div className="flex text-amber-400">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-3.5 w-3.5 ${i < Math.floor(product.rating || 0) ? "fill-amber-400" : "fill-gray-200"}`}
                              />
                            ))}
                          </div>
                          <span className="text-xs text-gray-500">({product.reviewCount || 0})</span>
                        </div>

                        <Link href={`/client-portal/shop/${product.id}`} className="block">
                          <h3 className="font-medium mb-1 group-hover:text-pink-600 transition-colors">
                            {product.name}
                          </h3>
                        </Link>

                        <p className="text-sm text-gray-500 mb-3 line-clamp-2">
                          {product.description}
                        </p>

                        <div className="flex justify-between items-center">
                          <div>
                            {product.isSale && product.salePrice ? (
                              <div className="flex items-center gap-2">
                                <span className="font-bold"><CurrencyDisplay amount={product.salePrice || 0} /></span>
                                <span className="text-sm text-gray-500 line-through"><CurrencyDisplay amount={product.price} /></span>
                              </div>
                            ) : (
                              <span className="font-bold"><CurrencyDisplay amount={product.price} /></span>
                            )}
                          </div>

                          <Button
                            size="sm"
                            className="bg-pink-600 hover:bg-pink-700"
                            onClick={() => handleAddToCart(product)}
                          >
                            <ShoppingBag className="h-4 w-4 mr-1" />
                            Add
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
  )
}
