"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { useProducts } from "@/lib/product-provider"
import { ProductCategory } from "@/lib/products-data"
import { Plus, Edit, Trash2, Package } from "lucide-react"

interface CategoryManagementDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CategoryManagementDialog({ open, onOpenChange }: CategoryManagementDialogProps) {
  const { toast } = useToast()
  const { categories, addCategory, updateCategory, deleteCategory, getProductsByCategory } = useProducts()
  const [activeTab, setActiveTab] = useState("list")
  const [editingCategory, setEditingCategory] = useState<ProductCategory | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    isActive: true,
  })

  const handleChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      isActive: true,
    })
    setEditingCategory(null)
  }

  const handleEdit = (category: ProductCategory) => {
    setEditingCategory(category)
    setFormData({
      name: category.name,
      description: category.description || "",
      isActive: category.isActive,
    })
    setActiveTab("form")
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      if (editingCategory) {
        // Update existing category
        const updatedCategory: ProductCategory = {
          ...editingCategory,
          name: formData.name,
          description: formData.description,
          isActive: formData.isActive,
          updatedAt: new Date(),
        }
        updateCategory(updatedCategory)

        toast({
          title: "Category updated",
          description: `${formData.name} has been updated successfully.`,
        })
      } else {
        // Create new category
        addCategory({
          name: formData.name,
          description: formData.description,
          isActive: formData.isActive,
          productCount: 0,
        })

        toast({
          title: "Category created",
          description: `${formData.name} has been added to your product categories.`,
        })
      }

      resetForm()
      setActiveTab("list")
    } catch (error) {
      console.error("Failed to save category:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to save category. Please try again.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDelete = async (category: ProductCategory) => {
    const productsInCategory = getProductsByCategory(category.name)

    if (productsInCategory.length > 0) {
      toast({
        variant: "destructive",
        title: "Cannot delete category",
        description: `This category contains ${productsInCategory.length} product(s). Please move or delete the products first.`,
      })
      return
    }

    if (window.confirm(`Are you sure you want to delete the category "${category.name}"?`)) {
      const success = deleteCategory(category.id)
      if (success) {
        toast({
          title: "Category deleted",
          description: `${category.name} has been deleted successfully.`,
        })
      }
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Manage Product Categories</DialogTitle>
          <DialogDescription>
            Create, edit, and organize your product categories for better inventory management.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mt-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="list">Categories</TabsTrigger>
            <TabsTrigger value="form">
              {editingCategory ? "Edit Category" : "Add Category"}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="list" className="space-y-4 mt-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">All Categories ({categories.length})</h3>
              <Button
                onClick={() => {
                  resetForm()
                  setActiveTab("form")
                }}
                size="sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Category
              </Button>
            </div>

            <div className="grid gap-3">
              {categories.map((category) => {
                const productsInCategory = getProductsByCategory(category.name)
                return (
                  <Card key={category.id}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium">{category.name}</h4>
                            {!category.isActive && (
                              <Badge variant="secondary">Inactive</Badge>
                            )}
                          </div>
                          {category.description && (
                            <p className="text-sm text-muted-foreground mb-2">
                              {category.description}
                            </p>
                          )}
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Package className="h-3 w-3" />
                              {productsInCategory.length} products
                            </div>
                            <span>
                              Created {new Date(category.createdAt).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(category)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(category)}
                            disabled={productsInCategory.length > 0}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            {categories.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No categories found</p>
                <p className="text-sm">Create your first category to get started</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="form" className="space-y-4 mt-4">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="categoryName">Category Name *</Label>
                <Input
                  id="categoryName"
                  value={formData.name}
                  onChange={(e) => handleChange("name", e.target.value)}
                  required
                  placeholder="Enter category name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="categoryDescription">Description</Label>
                <Textarea
                  id="categoryDescription"
                  value={formData.description}
                  onChange={(e) => handleChange("description", e.target.value)}
                  rows={3}
                  placeholder="Describe this category (optional)"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => handleChange("isActive", checked)}
                />
                <Label htmlFor="isActive">Active (visible in product forms)</Label>
              </div>

              <div className="flex gap-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    resetForm()
                    setActiveTab("list")
                  }}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting} className="flex-1">
                  {isSubmitting
                    ? "Saving..."
                    : editingCategory
                    ? "Update Category"
                    : "Create Category"}
                </Button>
              </div>
            </form>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
